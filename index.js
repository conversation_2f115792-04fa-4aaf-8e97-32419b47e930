import { PdfReader } from "pdfreader";
import fs from 'fs';

class PDFTableExtractor {
  constructor() {
    this.rows = [];
    this.currentRow = {};
    this.maxY = 0;
    this.tolerance = 2; // Y坐标容差，用于判断是否在同一行
  }

  // 提取PDF中的表格数据
  async extractTables(pdfPath) {
    return new Promise((resolve, reject) => {
      const items = [];

      new PdfReader().parseFileItems(pdfPath, (err, item) => {
        if (err) {
          console.error("解析PDF出错:", err);
          reject(err);
          return;
        }

        if (!item) {
          // 解析完成，处理收集到的数据
          this.processItems(items);
          resolve(this.formatTables());
          return;
        }

        if (item.text) {
          items.push({
            text: item.text,
            x: item.x,
            y: item.y,
            width: item.width,
            height: item.height
          });
        }
      });
    });
  }

  // 处理提取的文本项，按行分组
  processItems(items) {
    // 按Y坐标排序
    items.sort((a, b) => a.y - b.y);

    let currentY = -1;
    let currentRowItems = [];

    items.forEach(item => {
      // 如果Y坐标差异超过容差，说明是新的一行
      if (currentY === -1 || Math.abs(item.y - currentY) > this.tolerance) {
        if (currentRowItems.length > 0) {
          this.processRow(currentRowItems);
        }
        currentRowItems = [item];
        currentY = item.y;
      } else {
        currentRowItems.push(item);
      }
    });

    // 处理最后一行
    if (currentRowItems.length > 0) {
      this.processRow(currentRowItems);
    }
  }

  // 处理单行数据，按X坐标排序
  processRow(rowItems) {
    // 按X坐标排序
    rowItems.sort((a, b) => a.x - b.x);

    const row = rowItems.map(item => item.text.trim()).filter(text => text.length > 0);
    if (row.length > 0) {
      this.rows.push(row);
    }
  }

  // 格式化表格数据
  formatTables() {
    if (this.rows.length === 0) {
      return { message: "未找到表格数据" };
    }

    return {
      totalRows: this.rows.length,
      tables: this.detectTables(),
      allRows: this.rows
    };
  }

  // 检测和分离多个表格
  detectTables() {
    const tables = [];
    let currentTable = [];
    let expectedColumns = 0;

    this.rows.forEach((row, index) => {
      if (index === 0) {
        expectedColumns = row.length;
        currentTable.push(row);
      } else {
        // 如果列数差异很大，可能是新表格的开始
        if (Math.abs(row.length - expectedColumns) > 2 && currentTable.length > 0) {
          tables.push({
            headers: currentTable[0],
            data: currentTable.slice(1),
            rowCount: currentTable.length
          });
          currentTable = [row];
          expectedColumns = row.length;
        } else {
          currentTable.push(row);
        }
      }
    });

    // 添加最后一个表格
    if (currentTable.length > 0) {
      tables.push({
        headers: currentTable[0],
        data: currentTable.slice(1),
        rowCount: currentTable.length
      });
    }

    return tables;
  }

  // 导出为CSV格式
  exportToCSV(tables, outputPath) {
    let csvContent = '';

    tables.forEach((table, tableIndex) => {
      csvContent += `\n=== 表格 ${tableIndex + 1} ===\n`;

      // 添加表头
      csvContent += table.headers.join(',') + '\n';

      // 添加数据行
      table.data.forEach(row => {
        csvContent += row.join(',') + '\n';
      });

      csvContent += '\n';
    });

    fs.writeFileSync(outputPath, csvContent, 'utf8');
    console.log(`表格数据已导出到: ${outputPath}`);
  }

  // 导出为JSON格式
  exportToJSON(result, outputPath) {
    fs.writeFileSync(outputPath, JSON.stringify(result, null, 2), 'utf8');
    console.log(`表格数据已导出到: ${outputPath}`);
  }
}

// 使用示例
async function main() {
  const extractor = new PDFTableExtractor();

  try {
    console.log("开始提取PDF表格...");
    const result = await extractor.extractTables("files/LC100零件图册图册20160623.pdf");

    console.log(`\n提取完成！共找到 ${result.totalRows} 行数据`);
    console.log(`检测到 ${result.tables.length} 个表格\n`);

    // 显示表格信息
    result.tables.forEach((table, index) => {
      console.log(`=== 表格 ${index + 1} ===`);
      console.log(`表头: ${table.headers.join(' | ')}`);
      console.log(`数据行数: ${table.data.length}`);

      // 显示前3行数据作为预览
      console.log("数据预览:");
      table.data.slice(0, 3).forEach((row, rowIndex) => {
        console.log(`  ${rowIndex + 1}: ${row.join(' | ')}`);
      });

      if (table.data.length > 3) {
        console.log(`  ... 还有 ${table.data.length - 3} 行数据`);
      }
      console.log("");
    });

    // 导出数据
    extractor.exportToJSON(result, 'output/tables.json');
    extractor.exportToCSV(result.tables, 'output/tables.csv');

  } catch (error) {
    console.error("提取表格时出错:", error);
  }
}

// 创建输出目录
if (!fs.existsSync('output')) {
  fs.mkdirSync('output');
}

main();