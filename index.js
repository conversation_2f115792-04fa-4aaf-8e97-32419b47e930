import { PdfReader } from "pdfreader";
import fs from 'fs';

class PDFTableExtractor {
  constructor() {
    this.rows = [];
    this.yTolerance = 2; // Y坐标容差，用于判断是否在同一行
    this.debugMode = true; // 开启调试模式
  }

  // 提取PDF中的表格数据
  async extractTables(pdfPath) {
    return new Promise((resolve, reject) => {
      const items = [];

      new PdfReader().parseFileItems(pdfPath, (err, item) => {
        if (err) {
          console.error("解析PDF出错:", err);
          reject(err);
          return;
        }

        if (!item) {
          // 解析完成，处理收集到的数据
          this.processItems(items);
          resolve(this.formatTables());
          return;
        }

        if (item.text) {
          items.push({
            text: item.text.trim(),
            x: Math.round(item.x * 10) / 10, // 四舍五入减少坐标误差
            y: Math.round(item.y * 10) / 10,
            width: item.width,
            height: item.height
          });
        }
      });
    });
  }

  // 处理提取的文本项，智能合并列
  processItems(items) {
    // 过滤掉空文本
    items = items.filter(item => item.text && item.text.length > 0);

    if (this.debugMode) {
      console.log(`总共提取到 ${items.length} 个文本项`);
    }

    // 按Y坐标排序
    items.sort((a, b) => a.y - b.y);

    // 按行分组
    const rows = this.groupItemsByRows(items);

    // 智能合并列
    this.mergeColumns(rows);

    if (this.debugMode) {
      console.log(`最终处理得到 ${this.rows.length} 行数据`);
    }
  }

  // 按行分组
  groupItemsByRows(items) {
    const rows = [];
    let currentY = -1;
    let currentRowItems = [];

    items.forEach(item => {
      if (currentY === -1 || Math.abs(item.y - currentY) > this.yTolerance) {
        if (currentRowItems.length > 0) {
          // 按X坐标排序当前行
          currentRowItems.sort((a, b) => a.x - b.x);
          rows.push(currentRowItems);
        }
        currentRowItems = [item];
        currentY = item.y;
      } else {
        currentRowItems.push(item);
      }
    });

    // 处理最后一行
    if (currentRowItems.length > 0) {
      currentRowItems.sort((a, b) => a.x - b.x);
      rows.push(currentRowItems);
    }

    return rows;
  }

  // 智能合并列 - 基于X坐标间距
  mergeColumns(rows) {
    const columnGap = 15; // 列间距阈值

    rows.forEach(rowItems => {
      const mergedRow = [];
      let currentColumn = '';
      let lastX = -1;

      rowItems.forEach(item => {
        // 如果X坐标间距大于阈值，说明是新的一列
        if (lastX !== -1 && (item.x - lastX) > columnGap) {
          if (currentColumn.trim()) {
            mergedRow.push(currentColumn.trim());
          }
          currentColumn = item.text;
        } else {
          // 合并到当前列
          if (currentColumn) {
            currentColumn += ' ' + item.text;
          } else {
            currentColumn = item.text;
          }
        }
        lastX = item.x + (item.width || 0);
      });

      // 添加最后一列
      if (currentColumn.trim()) {
        mergedRow.push(currentColumn.trim());
      }

      // 只保留有效的行（至少2列）
      if (mergedRow.length >= 2) {
        this.rows.push(mergedRow);
      }
    });
  }



  // 格式化表格数据
  formatTables() {
    if (this.rows.length === 0) {
      return { message: "未找到表格数据" };
    }

    // 先尝试不过滤，看看原始数据
    if (this.debugMode) {
      console.log("原始行数据示例:");
      this.rows.slice(0, 5).forEach((row, i) => {
        console.log(`  行${i + 1} (${row.length}列): ${row.join(' | ')}`);
      });
    }

    // 过滤掉明显不是表格数据的行（比如只有1列的行）
    const filteredRows = this.rows.filter(row => row.length >= 2); // 至少2列才算表格

    if (this.debugMode) {
      console.log(`过滤后剩余 ${filteredRows.length} 行`);
    }

    return {
      totalRows: filteredRows.length,
      tables: this.detectTables(filteredRows),
      allRows: filteredRows
    };
  }

  // 检测和分离多个表格
  detectTables(rows) {
    if (rows.length === 0) return [];

    const tables = [];
    let currentTable = [];
    let expectedColumns = 0;
    let headerPattern = null;

    rows.forEach((row, index) => {
      // 检查是否是表头行（包含"序号"、"图号"、"名称"、"数量"等关键词）
      const isHeaderRow = this.isHeaderRow(row);

      if (isHeaderRow && currentTable.length > 0) {
        // 遇到新的表头，保存当前表格
        tables.push(this.createTableObject(currentTable));
        currentTable = [row];
        expectedColumns = row.length;
        headerPattern = this.getHeaderPattern(row);
      } else if (index === 0 || currentTable.length === 0) {
        // 第一行或新表格的开始
        currentTable.push(row);
        expectedColumns = row.length;
        if (isHeaderRow) {
          headerPattern = this.getHeaderPattern(row);
        }
      } else {
        // 检查行是否符合当前表格的结构
        if (this.isRowCompatible(row, expectedColumns, headerPattern)) {
          currentTable.push(row);
        } else if (currentTable.length > 0) {
          // 结构不匹配，可能是新表格
          tables.push(this.createTableObject(currentTable));
          currentTable = [row];
          expectedColumns = row.length;
          headerPattern = isHeaderRow ? this.getHeaderPattern(row) : null;
        }
      }
    });

    // 添加最后一个表格
    if (currentTable.length > 0) {
      tables.push(this.createTableObject(currentTable));
    }

    return tables;
  }

  // 检查是否是表头行
  isHeaderRow(row) {
    const headerKeywords = ['序号', '图号', '名称', '数量', 'Item', 'Part No', 'Description', 'Qty', 'Parts No', 'Part Name'];
    const rowText = row.join(' ').toLowerCase();

    return headerKeywords.some(keyword =>
      rowText.includes(keyword.toLowerCase()) ||
      row.some(cell => cell.toLowerCase().includes(keyword.toLowerCase()))
    );
  }

  // 获取表头模式
  getHeaderPattern(headerRow) {
    return {
      columnCount: headerRow.length,
      hasItemNumber: headerRow.some(cell => /序号|item/i.test(cell)),
      hasPartNumber: headerRow.some(cell => /图号|part\s*no/i.test(cell)),
      hasDescription: headerRow.some(cell => /名称|description|name/i.test(cell)),
      hasQuantity: headerRow.some(cell => /数量|qty/i.test(cell))
    };
  }

  // 检查行是否与表格兼容
  isRowCompatible(row, expectedColumns, headerPattern) {
    // 放宽列数限制
    if (Math.abs(row.length - expectedColumns) > 5) {
      return false;
    }

    // 如果有表头模式，检查数据是否符合
    if (headerPattern) {
      // 放宽长文本限制
      const hasLongText = row.some(cell => cell.length > 100);
      if (hasLongText) return false;

      // 不强制要求数字，因为有些行可能只有文本描述
      // const hasNumbers = row.some(cell => /\d/.test(cell));
      // if (headerPattern.hasItemNumber || headerPattern.hasQuantity) {
      //   return hasNumbers;
      // }
    }

    return true;
  }

  // 创建表格对象
  createTableObject(tableRows) {
    if (tableRows.length === 0) return null;

    const headers = tableRows[0];
    const data = tableRows.slice(1);

    return {
      headers: headers,
      data: data,
      rowCount: tableRows.length,
      columnCount: headers.length
    };
  }

  // 导出为CSV格式
  exportToCSV(tables, outputPath) {
    let csvContent = '';

    tables.forEach((table, tableIndex) => {
      csvContent += `\n=== 表格 ${tableIndex + 1} ===\n`;

      // 添加表头
      csvContent += table.headers.join(',') + '\n';

      // 添加数据行
      table.data.forEach(row => {
        csvContent += row.join(',') + '\n';
      });

      csvContent += '\n';
    });

    fs.writeFileSync(outputPath, csvContent, 'utf8');
    console.log(`表格数据已导出到: ${outputPath}`);
  }

  // 导出为JSON格式
  exportToJSON(result, outputPath) {
    fs.writeFileSync(outputPath, JSON.stringify(result, null, 2), 'utf8');
    console.log(`表格数据已导出到: ${outputPath}`);
  }

  // 导出结构化表格数据
  exportStructuredTables(tables, outputPath) {
    const structuredData = tables.map((table, index) => {
      if (!table) return null;

      const structuredTable = {
        tableIndex: index + 1,
        columnCount: table.columnCount,
        headers: table.headers,
        rowCount: table.data.length,
        data: table.data.map((row, rowIndex) => {
          const rowObject = {};
          table.headers.forEach((header, colIndex) => {
            rowObject[header] = row[colIndex] || '';
          });
          rowObject._rowIndex = rowIndex + 1;
          return rowObject;
        })
      };

      return structuredTable;
    }).filter(table => table !== null);

    fs.writeFileSync(outputPath, JSON.stringify(structuredData, null, 2), 'utf8');
    console.log(`结构化表格数据已导出到: ${outputPath}`);
  }
}

// 使用示例
async function main() {
  const extractor = new PDFTableExtractor();

  try {
    console.log("开始提取PDF表格...");
    const result = await extractor.extractTables("files/LC100零件图册图册20160623.pdf");

    if (result.message) {
      console.log(result.message);
      return;
    }

    console.log(`\n提取完成！`);
    console.log(`检测到 ${result.columnCount} 个列边界`);
    console.log(`共找到 ${result.totalRows} 行有效数据`);
    console.log(`识别出 ${result.tables.length} 个表格\n`);

    // 显示表格信息
    result.tables.forEach((table, index) => {
      if (!table) return;

      console.log(`=== 表格 ${index + 1} ===`);
      console.log(`列数: ${table.columnCount}`);
      console.log(`表头: ${table.headers.join(' | ')}`);
      console.log(`数据行数: ${table.data.length}`);

      // 显示前5行数据作为预览
      console.log("\n数据预览:");
      console.log("表头:", table.headers.map((h, i) => `[${i}]${h}`).join(' | '));

      table.data.slice(0, 5).forEach((row, rowIndex) => {
        const formattedRow = row.map((cell, i) => `[${i}]${cell}`).join(' | ');
        console.log(`  ${rowIndex + 1}: ${formattedRow}`);
      });

      if (table.data.length > 5) {
        console.log(`  ... 还有 ${table.data.length - 5} 行数据`);
      }
      console.log("");
    });

    // 导出数据
    extractor.exportToJSON(result, 'output/tables_improved.json');
    extractor.exportToCSV(result.tables, 'output/tables_improved.csv');

    // 导出结构化的表格数据
    extractor.exportStructuredTables(result.tables, 'output/structured_tables.json');

  } catch (error) {
    console.error("提取表格时出错:", error);
  }
}

// 创建输出目录
if (!fs.existsSync('output')) {
  fs.mkdirSync('output');
}

main();