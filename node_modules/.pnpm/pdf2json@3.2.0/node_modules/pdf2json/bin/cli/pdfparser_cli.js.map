{"version": 3, "file": "pdfparser_cli.js", "sources": ["../../../src/cli/p2jcliarg.ts", "../../../src/cli/p2jcli.ts"], "sourcesContent": [null, null], "names": ["pkInfo", "_PARSER_SIG", "_PRO_TIMER", "PDF<PERSON><PERSON><PERSON>", "CL<PERSON>rg<PERSON><PERSON><PERSON>", "args", "aliases", "usageMsg", "parsedArgv", "constructor", "Array", "isArray", "this", "usage", "alias", "key", "name", "description", "showHelp", "helpMsg", "value", "Object", "entries", "console", "log", "argv", "parseArgv", "isNumber", "x", "test", "setArg", "val", "Number", "<PERSON><PERSON><PERSON>", "split", "<PERSON><PERSON><PERSON>", "length", "akey", "avalue", "push", "for<PERSON>ach", "obj", "keys", "o", "i", "undefined", "prototype", "String", "arg", "extractKey", "match", "warn", "next", "slice", "yargs", "process", "ParserStream", "StringifyStream", "ONLY_SHOW_VERSION", "ONLY_SHOW_HELP", "VERBOSITY_LEVEL", "HAS_INPUT_DIR_OR_FILE", "PROCESS_RAW_TEXT_CONTENT", "PROCESS_FIELDS_CONTENT", "PROCESS_MER<PERSON>_<PERSON><PERSON>EN_TEXT_BLOCKS", "PROCESS_WITH_STREAM", "SINGLETON_PDF_PARSER", "INPUT_DIR_OR_FILE", "f", "PDFProcessor", "inputDir", "inputFile", "inputPath", "outputDir", "outputFile", "outputPath", "pdf<PERSON><PERSON><PERSON>", "curCLI", "path", "normalize", "join", "generateMergedTextBlocksStream", "Promise", "resolve", "reject", "outputStream", "createOutputStream", "replace", "getMergedTextBlocksStream", "pipe", "generateRawTextContentStream", "getRawTextContentStream", "generateFieldsTypesStream", "getAllFieldsTypesStream", "processAdditionalStreams", "outputTasks", "allSettled", "onPrimarySuccess", "addResultCount", "then", "retVal", "catch", "err", "onPrimaryError", "parseOnePDFStream", "on", "evtData", "parserE<PERSON>r", "fs", "createWriteStream", "info", "createReadStream", "createParserStream", "parseOnePDF", "writeFile", "JSON", "stringify", "loadPDF", "validateParams", "existsSync", "promises", "mkdir", "recursive", "inExtName", "extname", "toLowerCase", "basename", "fod", "openSync", "closeSync", "unlinkSync", "destroy", "processFile", "validateMsg", "call", "getOutputFile", "PDFCLI", "inputCount", "successCount", "failedCount", "warningCount", "statusMsgs", "initialize", "nodeUtil", "verbosity", "version", "error", "e", "message", "start", "time", "inputStatus", "statSync", "isFile", "processOneFile", "dirname", "isDirectory", "processOneDirectory", "complete", "nextTick", "timeEnd", "p2j", "addStatusMsg", "ret", "finally", "processFiles", "files", "allPromises", "file", "idx", "readdir", "toString", "_iChars", "pdfFiles", "filter", "indexOf", "substring", "oneMsg"], "mappings": "gHAEA,MAAMA,OAAEA,EAAQC,YAAaC,GAAeC,QAwB/BC,EACZC,KAAkB,GACVC,QAAiB,CAAA,EAEjBC,SAAW,GACXC,WAA6B,KAGrC,WAAAC,CAAYJ,GACPK,MAAMC,QAAQN,KAAOO,KAAKP,KAAOA,GAGtC,KAAAQ,CAAMN,GAEL,OADAK,KAAKL,SAAW,GAAGA,kBACZK,KAGR,KAAAE,CAAMC,EAAYC,EAAaC,GAE9B,OADAL,KAAKN,QAAQS,GAAO,CAAEC,OAAMC,eACrBL,KAGR,QAAAM,GACC,IAAIC,EAAUP,KAAKL,SACnB,IAAK,MAAOQ,EAAKK,KAAUC,OAAOC,QAAQV,KAAKN,SAAU,CACxD,MAAMU,KAAEA,EAAIC,YAAEA,GAAgBG,EAC9BD,GAAW,IAAIJ,OAASC,OAAUC,MAEnCM,QAAQC,IAAIL,GAGb,QAAIM,GACH,OAAOb,KAAKJ,WAAaI,KAAKJ,WAAaI,KAAKc,YAGjD,eAAOC,CAASC,GACf,MAAiB,iBAANA,MACP,iBAAiBC,KAAKD,IACnB,6CAA6CC,KAAKD,IAGlD,MAAAE,CAAOf,EAAagB,EAAUN,GACrC,MAAML,EAAQhB,EAAauB,SAASI,GAAOC,OAAOD,GAAOA,EACzDnB,KAAKqB,OAAOR,EAAMV,EAAImB,MAAM,KAAMd,GAElC,MAAMe,EAAWpB,KAAOH,KAAKN,QAAU,CAACM,KAAKN,QAAQS,GAAKC,MAAQ,GAClE,GAAImB,EAASC,OAAS,EACrB,IAAK,MAAOC,EAAMC,KAAWjB,OAAOC,QAAQV,KAAKN,SAChD,GAAIS,IAAQuB,EAAOtB,KAAM,CACxBmB,EAASI,KAAKF,GACd,MAIHF,EAASK,SAASZ,GAAMhB,KAAKqB,OAAOR,EAAMG,EAAEM,MAAM,KAAMd,KAGjD,MAAAa,CAAOQ,EAAaC,EAAgBtB,GAC3C,IAAIuB,EAASF,EACb,IAAK,IAAIG,EAAI,EAAGA,EAAIF,EAAKN,OAAS,EAAGQ,IAAK,CACzC,MAAM7B,EAAM2B,EAAKE,GACjB,GAAY,cAAR7B,EAAqB,YACV8B,IAAXF,EAAE5B,KAAoB4B,EAAE5B,GAAO,CAAA,GAElC4B,EAAE5B,KAASM,OAAOyB,WAClBH,EAAE5B,KAASiB,OAAOc,WAClBH,EAAE5B,KAASgC,OAAOD,YAElBH,EAAE5B,GAAO,CAAA,GACN4B,EAAE5B,KAASL,MAAMoC,YAAWH,EAAE5B,GAAO,IACzC4B,EAAIA,EAAE5B,GAGP,MAAMA,EAAM2B,EAAKA,EAAKN,OAAS,GACnB,cAARrB,IAEH4B,IAAMtB,OAAOyB,WACbH,IAAMX,OAAOc,WACbH,IAAMI,OAAOD,YAEbH,EAAI,CAAA,GACDA,IAAMjC,MAAMoC,YAAWH,EAAI,SAChBE,IAAXF,EAAE5B,GACL4B,EAAE5B,GAAOK,EACCV,MAAMC,QAAQgC,EAAE5B,IAC1B4B,EAAE5B,GAAKwB,KAAKnB,GAEZuB,EAAE5B,GAAO,CAAC4B,EAAE5B,GAAMK,IAIZ,SAAAM,GACP,MAAMrB,KAAEA,GAASO,KACXa,EAAO,CAAA,EAEb,IAAK,IAAImB,EAAI,EAAGA,EAAIvC,EAAK+B,OAAQQ,IAAK,CACrC,MAAMI,EAAM3C,EAAKuC,GAEjB,GAAI,QAAQf,KAAKmB,GAAM,CACtB,MAAMC,EAAaD,EAAIE,MAAM,WAC7B,IAAKxC,MAAMC,QAAQsC,GAAa,CAC/B1B,QAAQ4B,KAAK,sBAAuBH,GACpC,SAED,MAAMjC,EAAMkC,EAAW,GACjBG,EAAO/C,EAAKuC,EAAI,QACTC,IAATO,GAAuB,KAAKvB,KAAKuB,GAG1B,iBAAiBvB,KAAKuB,IAChCxC,KAAKkB,OAAOf,EAAc,SAATqC,EAAiB3B,GAClCmB,KAEAhC,KAAKkB,OAAOf,GAAK,EAAMU,IANvBb,KAAKkB,OAAOf,EAAKqC,EAAM3B,GACvBmB,UAOK,GAAI,UAAUf,KAAKmB,GAAM,CAC/B,MAAMjC,EAAMiC,EAAIK,OAAO,GAAG,GACd,MAARtC,IACCV,EAAKuC,EAAI,KAAO,cAAcf,KAAKxB,EAAKuC,EAAI,KAC/ChC,KAAKkB,OAAOf,EAAKV,EAAKuC,EAAI,GAAInB,GAC9BmB,KACUvC,EAAKuC,EAAI,IAAM,iBAAiBf,KAAKxB,EAAKuC,EAAI,KACxDhC,KAAKkB,OAAOf,EAAqB,SAAhBV,EAAKuC,EAAI,GAAenB,GACzCmB,KAEAhC,KAAKkB,OAAOf,GAAK,EAAMU,SAIzBF,QAAQ4B,KAAK,sBAAuBH,GAKtC,OADApC,KAAKJ,WAAaiB,EACXA,GAIF,MAAM6B,EAAQ,IAAIlD,EAAamD,QAAQ9B,KAAK4B,MAAM,IACvDxC,MAAM,KAAKX,eAAwBF,EAAOgB,kCAC1CF,MAAM,IAAK,UAAW,oBACtBA,MAAM,IAAK,OAAQ,mCACnBA,MACA,IACA,OACA,sMAEAA,MACA,IACA,SACA,uJAEAA,MACA,IACA,SACA,uEAEAA,MACA,IACA,aACA,6FAEAA,MACA,IACA,UACA,8FAEAA,MACA,IACA,QACA,gHAEAA,MACA,IACA,SACA,kHACCA,MACD,KACA,YACA,kFCrMI0C,aAAEA,EAAYC,gBAAEA,EAAezD,OAAEA,EAAQC,YAAaC,GAAeC,GAErEsB,KAAEA,GAAS6B,EACXI,EAAoB,MAAOjC,EAC3BkC,EAAiB,MAAOlC,EACxBmC,EAAkB,MAAOnC,EAAO,EAAI,EACpCoC,EAAwB,MAAOpC,EAE/BqC,EAA2B,MAAOrC,EAClCsC,EAAyB,MAAOtC,EAChCuC,EAAmC,MAAOvC,EAC1CwC,EAAsB,MAAOxC,EAC7ByC,EAAsB,OAAQzC,EAE9B0C,EAAoB1C,EAAK2C,EAE/B,MAAMC,EACGC,SAAW,GACXC,UAAY,GACZC,UAAY,GAEZC,UAAY,GACZC,WAAa,GACbC,WAAa,GAGbC,UAAiB,KAEjBC,OAAe,KAGvB,WAAApE,CAAY6D,EAAkBC,EAAmBM,GAEhDjE,KAAK0D,SAAWQ,EAAKC,UAAUT,GAC/B1D,KAAK2D,UAAYA,EACjB3D,KAAK4D,UAAYM,EAAKE,KAAKpE,KAAK0D,SAAU1D,KAAK2D,WAE/C3D,KAAK6D,UAAYK,EAAKC,UAAUtD,EAAKkB,GAAK2B,GAE1C1D,KAAKgE,UAAY,KACjBhE,KAAKiE,OAASA,EAIN,8BAAAI,GACR,OAAO,IAAIC,SAAQ,CAACC,EAASC,KAC5B,IAAKxE,KAAKgE,UAET,YADAQ,EAAO,wCAGR,MAAMC,EAAe7B,EAAa8B,mBACjC1E,KAAK+D,WAAWY,QAAQ,QAAS,gBACjCJ,EACAC,GAEDxE,KAAKgE,UACHY,4BACAC,KAAK,IAAIhC,GACTgC,KAAKJ,EAAa,IAId,4BAAAK,GACP,OAAO,IAAIR,SAAQ,CAACC,EAASC,KAC5B,MAAMC,EAAe7B,EAAa8B,mBACjC1E,KAAK+D,WAAWY,QAAQ,QAAS,gBACjCJ,EACAC,GAEDxE,KAAKgE,UAAUe,0BAA0BF,KAAKJ,EAAa,IAIrD,yBAAAO,GACP,OAAO,IAAIV,SAAQ,CAACC,EAASC,KAC5B,MAAMC,EAAe7B,EAAa8B,mBACjC1E,KAAK+D,WAAWY,QAAQ,QAAS,gBACjCJ,EACAC,GAEDxE,KAAKgE,UACHiB,0BACAJ,KAAK,IAAIhC,GACTgC,KAAKJ,EAAa,IAId,wBAAAS,GACP,MAAMC,EAAmC,GAazC,OAZIhC,GAEHgC,EAAYxD,KAAK3B,KAAKgF,6BAEnB9B,GAEHiC,EAAYxD,KAAK3B,KAAK8E,gCAEnB1B,GAEH+B,EAAYxD,KAAK3B,KAAKqE,kCAEhBC,QAAQc,WAAWD,GAGnB,gBAAAE,CAAiBd,EAA6BC,GACrDxE,KAAKiE,OAAOqB,gBAAe,GAC3BtF,KAAKkF,2BACHK,MAAMC,GAA4CjB,EAAQiB,KAC1DC,OAAOC,GAAalB,EAAOkB,KAGtB,cAAAC,CAAeD,EAAUlB,GAChCxE,KAAKiE,OAAOqB,eAAeI,GAC3BlB,EAAOkB,GAGA,iBAAAE,GACP,OAAO,IAAItB,SAAQ,CAACC,EAASC,MACxBlB,IAAyBtD,KAAKgE,YAAeV,KAEhDtD,KAAKgE,UAAY,IAAIzE,EAAU,KAAM2D,GACrClD,KAAKgE,UAAU6B,GAAG,uBAAwBC,GACzC9F,KAAK2F,eAAeG,EAAQC,YAAavB,MAI3C,MAAMC,EAAeuB,EAAGC,kBAAkBjG,KAAK+D,YAC/CU,EAAaoB,GAAG,UAAU,IAAM7F,KAAKqF,iBAAiBd,EAASC,KAC/DC,EAAaoB,GAAG,SAAUH,GAAQ1F,KAAK2F,eAAeD,EAAKlB,KAE3D7D,QAAQuF,KACP,sBAAsBlG,KAAK2D,kBAAkB3D,KAAK+D,cAE/BiC,EAAGG,iBAAiBnG,KAAK4D,WAE3CiB,KAAK7E,KAAKgE,UAAUoC,sBACpBvB,KAAK,IAAIhC,GACTgC,KAAKJ,EAAa,IAId,WAAA4B,GACP,OAAO,IAAI/B,SAAQ,CAACC,EAASC,MACxBlB,IAAyBtD,KAAKgE,YAAeV,KAEhDtD,KAAKgE,UAAY,IAAIzE,EAAU,KAAM2D,GACrClD,KAAKgE,UAAU6B,GAAG,uBAAwBC,GACzC9F,KAAK2F,eAAeG,EAAQC,YAAavB,MAI3CxE,KAAKgE,UAAU6B,GAAG,uBAAwBC,IACzCE,EAAGM,UAAUtG,KAAK+D,WAAYwC,KAAKC,UAAUV,IAAWJ,IACnDA,EACH1F,KAAK2F,eAAeD,EAAKlB,GAEzBxE,KAAKqF,iBAAiBd,EAASC,KAE/B,IAGH7D,QAAQuF,KACP,oBAAoBlG,KAAK2D,kBAAkB3D,KAAK+D,cAEjD/D,KAAKgE,UAAUyC,QAAQzG,KAAK4D,UAAWZ,EAAgB,IAKzD,oBAAM0D,GACL,IAAIlB,EAAS,GAEb,GAAKQ,EAAGW,WAAW3G,KAAK0D,UAGnB,GAAKsC,EAAGW,WAAW3G,KAAK4D,YAGxB,IAAKoC,EAAGW,WAAW3G,KAAK6D,WAC5B,UACOmC,EAAGY,SAASC,MAAM7G,KAAK6D,UAAW,CAAEiD,WAAW,YAEhDd,EAAGW,WAAW3G,KAAK6D,aACvB2B,EAAS,qEAAqExF,KAAK6D,oBAPrF2B,EACC,2CAA2CxF,KAAK4D,kBAJjD4B,EACC,gDAAgDxF,KAAK0D,YAavD,GAAe,KAAX8B,EAEH,OADAxF,KAAKiE,OAAOqB,eAAeE,GACpBA,EAGR,MAAMuB,EAAY7C,EAAK8C,QAAQhH,KAAK2D,WAAWsD,cAC/C,GAAkB,SAAdF,EACHvB,EACC,8DAA8DxF,KAAK2D,kBAKpE,GAFA3D,KAAK8D,WAAa,GAAGI,EAAKgD,SAASlH,KAAK4D,UAAWmD,UACnD/G,KAAK+D,WAAaG,EAAKC,UAAU,GAAGnE,KAAK6D,aAAa7D,KAAK8D,cACvDkC,EAAGW,WAAW3G,KAAK+D,YACtBpD,QAAQ4B,KAAK,kCAAkCvC,KAAK+D,kBAEhD,CACJ,MAAMoD,EAAMnB,EAAGoB,SAASpH,KAAK+D,WAAY,MACpCoD,GAEJnB,EAAGqB,UAAUF,GACbnB,EAAGsB,WAAWtH,KAAK+D,aAHVyB,EAAS,iCAAiCxF,KAAK+D,aAO3D,OAAOyB,EAGR,OAAA+B,GACCvH,KAAK0D,SAAW,GAChB1D,KAAK2D,UAAY,GACjB3D,KAAK4D,UAAY,GACjB5D,KAAK6D,UAAY,GACjB7D,KAAK+D,WAAa,GAEd/D,KAAKgE,WACRhE,KAAKgE,UAAUuD,UAEhBvH,KAAKgE,UAAY,KACjBhE,KAAKiE,OAAS,KAGf,WAAAuD,GACC,OAAO,IAAIlD,SAAQ,CAACC,EAASC,KAC5BxE,KAAK0G,iBACHnB,MAAMkC,IACN,GAAoB,KAAhBA,EACHjD,EAAOiD,OAEH,EACepE,EAChBrD,KAAK4F,kBACL5F,KAAKqG,aAENqB,KAAK1H,MACLuF,MAAM/E,GAAU+D,EAAQ/D,KACxBiF,OAAOC,GAAQlB,EAAOkB,SAGzBD,OAAOC,GAAQlB,EAAOkB,IAAK,IAI/BiC,cAAgB,IAAMzD,EAAKE,KAAKpE,KAAK6D,UAAW7D,KAAK8D,YAGxC,MAAO8D,EACpBC,WAAa,EACbC,aAAe,EACfC,YAAc,EACdC,aAAe,EACfC,WAAwB,GAGxB,WAAApI,GACCG,KAAK6H,WAAa,EAClB7H,KAAK8H,aAAe,EACpB9H,KAAK+H,YAAc,EACnB/H,KAAKgI,aAAe,EACpBhI,KAAKiI,WAAa,GAGnB,UAAAC,GAGCC,EAASC,UAAUpF,GACnB,IAAIwC,GAAS,EACb,IACK1C,GACHnC,QAAQC,IAAIxB,EAAOiJ,SACnB7C,GAAS,GACCzC,GACVL,EAAMpC,WACNkF,GAAS,GACEvC,IACXP,EAAMpC,WACNK,QAAQ2H,MAAM,sDACd9C,GAAS,GAET,MAAO+C,GACR5H,QAAQ2H,MAAM,cAAcC,EAAEC,WAC9BhD,GAAS,EAEV,OAAOA,EAGR,WAAMiD,GACL,GAAKzI,KAAKkI,cAAiB3E,EAA3B,CAKA5C,QAAQC,IAAItB,GACZqB,QAAQ+H,KAAKpJ,GAEb,IACC,MAAMqJ,EAAc3C,EAAG4C,SAASrF,GAC5BoF,EAAYE,UACf7I,KAAK6H,WAAa,QACZ7H,KAAK8I,eACV5E,EAAK6E,QAAQxF,GACbW,EAAKgD,SAAS3D,KAELoF,EAAYK,qBAChBhJ,KAAKiJ,oBAAoB/E,EAAKC,UAAUZ,IAE9C,MAAOgF,GACR5H,QAAQ2H,MAAM,cAAeC,WAE7BvI,KAAKkJ,iBArBLvI,QAAQ2H,MAAM,6BAyBhB,QAAAY,GACKlJ,KAAKiI,WAAWzG,OAAS,GAAGb,QAAQC,IAAIZ,KAAKiI,YACjDtH,QAAQC,IACP,GAAGZ,KAAK6H,2BAA2B7H,KAAK8H,yBAAyB9H,KAAK+H,qBAAqB/H,KAAKgI,wBAEjGrF,QAAQwG,UAAS,KAChBxI,QAAQyI,QAAQ9J,EAAW,IAK7B,cAAAwJ,CAAepF,EAAiBC,GAC/B,OAAO,IAAIW,SAAQ,CAACC,EAASC,KAC5B,MAAM6E,EAAM,IAAI5F,EAAaC,EAAUC,EAAW3D,MAClDqJ,EACE7B,cAEAjC,MAAMC,IACNxF,KAAKsJ,aACJ,KACA,GAAGpF,EAAKE,KAAKV,EAAUC,SAAiB0F,EAAI1B,mBAE7CnC,EAAO5D,SAAS2H,GAAYvJ,KAAKsJ,aAAa,KAAM,KAAKC,EAAI/I,WAC7D+D,EAAQiB,EAAO,IAEfC,OAAO6C,IACPtI,KAAKsJ,aACJhB,EACA,GAAGpE,EAAKE,KAAKV,EAAUC,SAAiB2E,KAEzC9D,EAAO8D,EAAM,IAEbkB,SAAQ,IAAMH,EAAI9B,WAAU,IAIhC,YAAAkC,CAAa/F,EAAkBgG,GAC9B,MAAMC,EAAkC,GAIxC,OAHAD,EAAM9H,SAAQ,CAACgI,EAAcC,IAC5BF,EAAYhI,KAAK3B,KAAK8I,eAAepF,EAAUkG,MAEzCtF,QAAQc,WAAWuE,GAG3B,mBAAAV,CAAoBvF,GACnB,OAAO,IAAIY,SAAQ,CAACC,EAASC,KAC5BwB,EAAG8D,QAAQpG,GAAU,CAACgC,EAAKgE,KAC1B,GAAIhE,EACH1F,KAAKsJ,cAAa,EAAM,IAAI5F,QAAegC,EAAIqE,cAC/CvF,EAAOkB,OACD,CACN,MAAMsE,EAAU,uCACVC,EAAWP,EAAMQ,QACrBN,GACiC,SAAjCA,EAAKnH,OAAO,GAAGwE,eACf+C,EAAQG,QAAQP,EAAKQ,UAAU,EAAG,IAAM,IAG1CpK,KAAK6H,WAAaoC,EAASzI,OACvBxB,KAAK6H,WAAa,EACrB7H,KAAKyJ,aAAa/F,EAAUuG,GAC1B1E,MAAM/E,GAAU+D,EAAQ/D,KACxBiF,OAAOC,GAAQlB,EAAOkB,MAExB1F,KAAKsJ,cAAa,EAAM,IAAI5F,2BAC5Ba,EAAQ,0BAGT,IAIJ,YAAA+E,CAAahB,EAAW+B,GACvBrK,KAAKiI,WAAWtG,KACf2G,EAAQ,aAAa+B,IAAW,eAAeA,KAIjD,cAAA/E,CAAegD,GACdA,EAAQtI,KAAK+H,cAAgB/H,KAAK8H"}