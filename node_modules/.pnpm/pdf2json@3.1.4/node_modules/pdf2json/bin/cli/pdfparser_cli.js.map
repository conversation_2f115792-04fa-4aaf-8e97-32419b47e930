{"version": 3, "file": "pdfparser_cli.js", "sources": ["../../../src/cli/p2jcliarg.ts", "../../../src/cli/p2jcli.ts"], "sourcesContent": [null, null], "names": ["pkInfo", "_PARSER_SIG", "_PRO_TIMER", "PDF<PERSON><PERSON><PERSON>", "CL<PERSON>rg<PERSON><PERSON><PERSON>", "args", "aliases", "usageMsg", "parsedArgv", "constructor", "Array", "isArray", "this", "usage", "alias", "key", "name", "description", "showHelp", "helpMsg", "value", "Object", "entries", "console", "log", "argv", "parseArgv", "isNumber", "x", "test", "setArg", "val", "Number", "<PERSON><PERSON><PERSON>", "split", "<PERSON><PERSON><PERSON>", "length", "akey", "avalue", "push", "for<PERSON>ach", "obj", "keys", "o", "i", "undefined", "prototype", "String", "arg", "extractKey", "match", "warn", "next", "slice", "yargs", "process", "ParserStream", "StringifyStream", "ONLY_SHOW_VERSION", "ONLY_SHOW_HELP", "VERBOSITY_LEVEL", "HAS_INPUT_DIR_OR_FILE", "PROCESS_RAW_TEXT_CONTENT", "PROCESS_FIELDS_CONTENT", "PROCESS_MER<PERSON>_<PERSON><PERSON>EN_TEXT_BLOCKS", "PROCESS_WITH_STREAM", "INPUT_DIR_OR_FILE", "f", "PDFProcessor", "inputDir", "inputFile", "inputPath", "outputDir", "outputFile", "outputPath", "pdf<PERSON><PERSON><PERSON>", "curCLI", "path", "normalize", "join", "generateMergedTextBlocksStream", "Promise", "resolve", "reject", "outputStream", "createOutputStream", "replace", "getMergedTextBlocksStream", "pipe", "generateRawTextContentStream", "getRawTextContentStream", "generateFieldsTypesStream", "getAllFieldsTypesStream", "processAdditionalStreams", "outputTasks", "allSettled", "onPrimarySuccess", "addResultCount", "then", "retVal", "catch", "err", "onPrimaryError", "parseOnePDFStream", "on", "evtData", "parserE<PERSON>r", "fs", "createWriteStream", "info", "createReadStream", "createParserStream", "parseOnePDF", "writeFile", "JSON", "stringify", "loadPDF", "validateParams", "existsSync", "promises", "mkdir", "recursive", "inExtName", "extname", "toLowerCase", "basename", "fod", "openSync", "closeSync", "unlinkSync", "destroy", "processFile", "validateMsg", "call", "getOutputFile", "PDFCLI", "inputCount", "successCount", "failedCount", "warningCount", "statusMsgs", "initialize", "nodeUtil", "verbosity", "version", "error", "e", "message", "start", "time", "inputStatus", "statSync", "isFile", "processOneFile", "dirname", "isDirectory", "processOneDirectory", "complete", "nextTick", "timeEnd", "p2j", "addStatusMsg", "ret", "finally", "processFiles", "files", "allPromises", "file", "idx", "readdir", "toString", "_iChars", "pdfFiles", "filter", "indexOf", "substring", "oneMsg"], "mappings": "iGAEA,MAAMA,OAAEA,EAAQC,YAAaC,GAAeC,QAuB/BC,EACZC,KAAkB,GACVC,QAAiB,CAAA,EAEjBC,SAAW,GACXC,WAA6B,KAGrC,WAAAC,CAAYJ,GACPK,MAAMC,QAAQN,KAAOO,KAAKP,KAAOA,EACrC,CAED,KAAAQ,CAAMN,GAEL,OADAK,KAAKL,SAAW,GAAGA,kBACZK,IACP,CAED,KAAAE,CAAMC,EAAYC,EAAaC,GAE9B,OADAL,KAAKN,QAAQS,GAAO,CAAEC,OAAMC,eACrBL,IACP,CAED,QAAAM,GACC,IAAIC,EAAUP,KAAKL,SACnB,IAAK,MAAOQ,EAAKK,KAAUC,OAAOC,QAAQV,KAAKN,SAAU,CACxD,MAAMU,KAAEA,EAAIC,YAAEA,GAAgBG,EAC9BD,GAAW,IAAIJ,OAASC,OAAUC,KAClC,CACDM,QAAQC,IAAIL,EACZ,CAED,QAAIM,GACH,OAAOb,KAAKJ,WAAaI,KAAKJ,WAAaI,KAAKc,WAChD,CAED,eAAOC,CAASC,GACf,MAAiB,iBAANA,MACP,iBAAiBC,KAAKD,IACnB,6CAA6CC,KAAKD,GACzD,CAEO,MAAAE,CAAOf,EAAagB,EAAUN,GACrC,MAAML,EAAQhB,EAAauB,SAASI,GAAOC,OAAOD,GAAOA,EACzDnB,KAAKqB,OAAOR,EAAMV,EAAImB,MAAM,KAAMd,GAElC,MAAMe,EAAWpB,KAAOH,KAAKN,QAAU,CAACM,KAAKN,QAAQS,GAAKC,MAAQ,GAClE,GAAImB,EAASC,OAAS,EACrB,IAAK,MAAOC,EAAMC,KAAWjB,OAAOC,QAAQV,KAAKN,SAChD,GAAIS,IAAQuB,EAAOtB,KAAM,CACxBmB,EAASI,KAAKF,GACd,KACA,CAGHF,EAASK,SAASZ,GAAMhB,KAAKqB,OAAOR,EAAMG,EAAEM,MAAM,KAAMd,IACxD,CAEO,MAAAa,CAAOQ,EAAaC,EAAgBtB,GAC3C,IAAIuB,EAASF,EACb,IAAK,IAAIG,EAAI,EAAGA,EAAIF,EAAKN,OAAS,EAAGQ,IAAK,CACzC,MAAM7B,EAAM2B,EAAKE,GACjB,GAAY,cAAR7B,EAAqB,YACV8B,IAAXF,EAAE5B,KAAoB4B,EAAE5B,GAAO,IAElC4B,EAAE5B,KAASM,OAAOyB,WAClBH,EAAE5B,KAASiB,OAAOc,WAClBH,EAAE5B,KAASgC,OAAOD,YAElBH,EAAE5B,GAAO,IACN4B,EAAE5B,KAASL,MAAMoC,YAAWH,EAAE5B,GAAO,IACzC4B,EAAIA,EAAE5B,EACN,CAED,MAAMA,EAAM2B,EAAKA,EAAKN,OAAS,GACnB,cAARrB,IAEH4B,IAAMtB,OAAOyB,WACbH,IAAMX,OAAOc,WACbH,IAAMI,OAAOD,YAEbH,EAAI,CAAA,GACDA,IAAMjC,MAAMoC,YAAWH,EAAI,SAChBE,IAAXF,EAAE5B,GACL4B,EAAE5B,GAAOK,EACCV,MAAMC,QAAQgC,EAAE5B,IAC1B4B,EAAE5B,GAAKwB,KAAKnB,GAEZuB,EAAE5B,GAAO,CAAC4B,EAAE5B,GAAMK,GAEnB,CAEO,SAAAM,GACP,MAAMrB,KAAEA,GAASO,KACXa,EAAO,CAAA,EAEb,IAAK,IAAImB,EAAI,EAAGA,EAAIvC,EAAK+B,OAAQQ,IAAK,CACrC,MAAMI,EAAM3C,EAAKuC,GAEjB,GAAI,QAAQf,KAAKmB,GAAM,CACtB,MAAMC,EAAaD,EAAIE,MAAM,WAC7B,IAAKxC,MAAMC,QAAQsC,GAAa,CAC/B1B,QAAQ4B,KAAK,sBAAuBH,GACpC,QACA,CACD,MAAMjC,EAAMkC,EAAW,GACjBG,EAAO/C,EAAKuC,EAAI,QACTC,IAATO,GAAuB,KAAKvB,KAAKuB,GAG1B,iBAAiBvB,KAAKuB,IAChCxC,KAAKkB,OAAOf,EAAc,SAATqC,EAAiB3B,GAClCmB,KAEAhC,KAAKkB,OAAOf,GAAK,EAAMU,IANvBb,KAAKkB,OAAOf,EAAKqC,EAAM3B,GACvBmB,IAOD,MAAM,GAAI,UAAUf,KAAKmB,GAAM,CAC/B,MAAMjC,EAAMiC,EAAIK,OAAO,GAAG,GACd,MAARtC,IACCV,EAAKuC,EAAI,KAAO,cAAcf,KAAKxB,EAAKuC,EAAI,KAC/ChC,KAAKkB,OAAOf,EAAKV,EAAKuC,EAAI,GAAInB,GAC9BmB,KACUvC,EAAKuC,EAAI,IAAM,iBAAiBf,KAAKxB,EAAKuC,EAAI,KACxDhC,KAAKkB,OAAOf,EAAqB,SAAhBV,EAAKuC,EAAI,GAAenB,GACzCmB,KAEAhC,KAAKkB,OAAOf,GAAK,EAAMU,GAGzB,MACAF,QAAQ4B,KAAK,sBAAuBH,EAErC,CAGD,OADApC,KAAKJ,WAAaiB,EACXA,CACP,EAGK,MAAM6B,EAAQ,IAAIlD,EAAamD,QAAQ9B,KAAK4B,MAAM,IACvDxC,MAAM,KAAKX,eAAwBF,EAAOgB,kCAC1CF,MAAM,IAAK,UAAW,oBACtBA,MAAM,IAAK,OAAQ,mCACnBA,MACA,IACA,OACA,sMAEAA,MACA,IACA,SACA,uJAEAA,MACA,IACA,SACA,uEAEAA,MACA,IACA,aACA,6FAEAA,MACA,IACA,UACA,8FAEAA,MACA,IACA,QACA,gHAEAA,MACA,IACA,SACA,mHChMI0C,aAAEA,EAAYC,gBAAEA,EAAezD,OAAEA,EAAQC,YAAaC,GAAeC,GAErEsB,KAAEA,GAAS6B,EACXI,EAAoB,MAAOjC,EAC3BkC,EAAiB,MAAOlC,EACxBmC,EAAkB,MAAOnC,EAAO,EAAI,EACpCoC,EAAwB,MAAOpC,EAE/BqC,EAA2B,MAAOrC,EAClCsC,EAAyB,MAAOtC,EAChCuC,EAAmC,MAAOvC,EAC1CwC,EAAsB,MAAOxC,EAE7ByC,EAAoBzC,EAAK0C,EAE/B,MAAMC,EACGC,SAAW,GACXC,UAAY,GACZC,UAAY,GAEZC,UAAY,GACZC,WAAa,GACbC,WAAa,GAGbC,UAAiB,KAEjBC,OAAe,KAGvB,WAAAnE,CAAY4D,EAAkBC,EAAmBM,GAEhDhE,KAAKyD,SAAWQ,EAAKC,UAAUT,GAC/BzD,KAAK0D,UAAYA,EACjB1D,KAAK2D,UAAYM,EAAKE,KAAKnE,KAAKyD,SAAUzD,KAAK0D,WAE/C1D,KAAK4D,UAAYK,EAAKC,UAAUrD,EAAKkB,GAAK0B,GAE1CzD,KAAK+D,UAAY,KACjB/D,KAAKgE,OAASA,CACd,CAGQ,8BAAAI,GACR,OAAO,IAAIC,SAAQ,CAACC,EAASC,KAC5B,IAAKvE,KAAK+D,UAET,YADAQ,EAAO,wCAGR,MAAMC,EAAe5B,EAAa6B,mBACjCzE,KAAK8D,WAAWY,QAAQ,QAAS,gBACjCJ,EACAC,GAEDvE,KAAK+D,UACHY,4BACAC,KAAK,IAAI/B,GACT+B,KAAKJ,EAAa,GAErB,CAEO,4BAAAK,GACP,OAAO,IAAIR,SAAQ,CAACC,EAASC,KAC5B,MAAMC,EAAe5B,EAAa6B,mBACjCzE,KAAK8D,WAAWY,QAAQ,QAAS,gBACjCJ,EACAC,GAEDvE,KAAK+D,UAAUe,0BAA0BF,KAAKJ,EAAa,GAE5D,CAEO,yBAAAO,GACP,OAAO,IAAIV,SAAQ,CAACC,EAASC,KAC5B,MAAMC,EAAe5B,EAAa6B,mBACjCzE,KAAK8D,WAAWY,QAAQ,QAAS,gBACjCJ,EACAC,GAEDvE,KAAK+D,UACHiB,0BACAJ,KAAK,IAAI/B,GACT+B,KAAKJ,EAAa,GAErB,CAEO,wBAAAS,GACP,MAAMC,EAAmC,GAazC,OAZI/B,GAEH+B,EAAYvD,KAAK3B,KAAK+E,6BAEnB7B,GAEHgC,EAAYvD,KAAK3B,KAAK6E,gCAEnBzB,GAEH8B,EAAYvD,KAAK3B,KAAKoE,kCAEhBC,QAAQc,WAAWD,EAC1B,CAEO,gBAAAE,CAAiBd,EAA6BC,GACrDvE,KAAKgE,OAAOqB,gBAAe,GAC3BrF,KAAKiF,2BACHK,MAAMC,GAA4CjB,EAAQiB,KAC1DC,OAAOC,GAAalB,EAAOkB,IAC7B,CAEO,cAAAC,CAAeD,EAAUlB,GAChCvE,KAAKgE,OAAOqB,eAAeI,GAC3BlB,EAAOkB,EACP,CAEO,iBAAAE,GACP,OAAO,IAAItB,SAAQ,CAACC,EAASC,KAC5BvE,KAAK+D,UAAY,IAAIxE,EAAU,KAAM2D,GACrClD,KAAK+D,UAAU6B,GAAG,uBAAwBC,GACzC7F,KAAK0F,eAAeG,EAAQC,YAAavB,KAG1C,MAAMC,EAAeuB,EAAGC,kBAAkBhG,KAAK8D,YAC/CU,EAAaoB,GAAG,UAAU,IAAM5F,KAAKoF,iBAAiBd,EAASC,KAC/DC,EAAaoB,GAAG,SAAUH,GAAQzF,KAAK0F,eAAeD,EAAKlB,KAE3D5D,QAAQsF,KACP,sBAAsBjG,KAAK0D,kBAAkB1D,KAAK8D,cAE/BiC,EAAGG,iBAAiBlG,KAAK2D,WAE3CiB,KAAK5E,KAAK+D,UAAUoC,sBACpBvB,KAAK,IAAI/B,GACT+B,KAAKJ,EAAa,GAErB,CAEO,WAAA4B,GACP,OAAO,IAAI/B,SAAQ,CAACC,EAASC,KAC5BvE,KAAK+D,UAAY,IAAIxE,EAAU,KAAM2D,GACrClD,KAAK+D,UAAU6B,GAAG,uBAAwBC,IACzC7F,KAAK0F,eAAeG,EAAQC,YAAavB,EAAO,IAGjDvE,KAAK+D,UAAU6B,GAAG,uBAAwBC,IACzCE,EAAGM,UAAUrG,KAAK8D,WAAYwC,KAAKC,UAAUV,IAAWJ,IACnDA,EACHzF,KAAK0F,eAAeD,EAAKlB,GAEzBvE,KAAKoF,iBAAiBd,EAASC,EAC/B,GACA,IAGH5D,QAAQsF,KACP,oBAAoBjG,KAAK0D,kBAAkB1D,KAAK8D,cAEjD9D,KAAK+D,UAAUyC,QAAQxG,KAAK2D,UAAWX,EAAgB,GAExD,CAGD,oBAAMyD,GACL,IAAIlB,EAAS,GAEb,GAAKQ,EAAGW,WAAW1G,KAAKyD,UAGnB,GAAKsC,EAAGW,WAAW1G,KAAK2D,YAGxB,IAAKoC,EAAGW,WAAW1G,KAAK4D,WAC5B,UACOmC,EAAGY,SAASC,MAAM5G,KAAK4D,UAAW,CAAEiD,WAAW,GACrD,CAAS,QACJd,EAAGW,WAAW1G,KAAK4D,aACvB2B,EAAS,qEAAqEvF,KAAK4D,aACpF,OARD2B,EACC,2CAA2CvF,KAAK2D,kBAJjD4B,EACC,gDAAgDvF,KAAKyD,YAavD,GAAe,KAAX8B,EAEH,OADAvF,KAAKgE,OAAOqB,eAAeE,GACpBA,EAGR,MAAMuB,EAAY7C,EAAK8C,QAAQ/G,KAAK0D,WAAWsD,cAC/C,GAAkB,SAAdF,EACHvB,EACC,8DAA8DvF,KAAK0D,kBAKpE,GAFA1D,KAAK6D,WAAa,GAAGI,EAAKgD,SAASjH,KAAK2D,UAAWmD,UACnD9G,KAAK8D,WAAaG,EAAKC,UAAU,GAAGlE,KAAK4D,aAAa5D,KAAK6D,cACvDkC,EAAGW,WAAW1G,KAAK8D,YACtBnD,QAAQ4B,KAAK,kCAAkCvC,KAAK8D,kBAEhD,CACJ,MAAMoD,EAAMnB,EAAGoB,SAASnH,KAAK8D,WAAY,MACpCoD,GAEJnB,EAAGqB,UAAUF,GACbnB,EAAGsB,WAAWrH,KAAK8D,aAHVyB,EAAS,iCAAiCvF,KAAK8D,YAKzD,CAEF,OAAOyB,CACP,CAED,OAAA+B,GACCtH,KAAKyD,SAAW,GAChBzD,KAAK0D,UAAY,GACjB1D,KAAK2D,UAAY,GACjB3D,KAAK4D,UAAY,GACjB5D,KAAK8D,WAAa,GAEd9D,KAAK+D,WACR/D,KAAK+D,UAAUuD,UAEhBtH,KAAK+D,UAAY,KACjB/D,KAAKgE,OAAS,IACd,CAED,WAAAuD,GACC,OAAO,IAAIlD,SAAQ,CAACC,EAASC,KAC5BvE,KAAKyG,iBACHnB,MAAMkC,IACN,GAAoB,KAAhBA,EACHjD,EAAOiD,OAEH,EACenE,EAChBrD,KAAK2F,kBACL3F,KAAKoG,aAENqB,KAAKzH,MACLsF,MAAM9E,GAAU8D,EAAQ9D,KACxBgF,OAAOC,GAAQlB,EAAOkB,IACxB,KAEDD,OAAOC,GAAQlB,EAAOkB,IAAK,GAE9B,CAEDiC,cAAgB,IAAMzD,EAAKE,KAAKnE,KAAK4D,UAAW5D,KAAK6D,YAGxC,MAAO8D,EACpBC,WAAa,EACbC,aAAe,EACfC,YAAc,EACdC,aAAe,EACfC,WAAwB,GAGxB,WAAAnI,GACCG,KAAK4H,WAAa,EAClB5H,KAAK6H,aAAe,EACpB7H,KAAK8H,YAAc,EACnB9H,KAAK+H,aAAe,EACpB/H,KAAKgI,WAAa,EAClB,CAED,UAAAC,GAGCC,EAASC,UAAUnF,GACnB,IAAIuC,GAAS,EACb,IACKzC,GACHnC,QAAQC,IAAIxB,EAAOgJ,SACnB7C,GAAS,GACCxC,GACVL,EAAMpC,WACNiF,GAAS,GACEtC,IACXP,EAAMpC,WACNK,QAAQ0H,MAAM,sDACd9C,GAAS,EAEV,CAAC,MAAO+C,GACR3H,QAAQ0H,MAAM,cAAcC,EAAEC,WAC9BhD,GAAS,CACT,CACD,OAAOA,CACP,CAED,WAAMiD,GACL,GAAKxI,KAAKiI,cAAiB3E,EAA3B,CAKA3C,QAAQC,IAAItB,GACZqB,QAAQ8H,KAAKnJ,GAEb,IACC,MAAMoJ,EAAc3C,EAAG4C,SAASrF,GAC5BoF,EAAYE,UACf5I,KAAK4H,WAAa,QACZ5H,KAAK6I,eACV5E,EAAK6E,QAAQxF,GACbW,EAAKgD,SAAS3D,KAELoF,EAAYK,qBAChB/I,KAAKgJ,oBAAoB/E,EAAKC,UAAUZ,GAE/C,CAAC,MAAOgF,GACR3H,QAAQ0H,MAAM,cAAeC,EAC7B,CAAS,QACTtI,KAAKiJ,UACL,CApBA,MAFAtI,QAAQ0H,MAAM,4BAuBf,CAED,QAAAY,GACKjJ,KAAKgI,WAAWxG,OAAS,GAAGb,QAAQC,IAAIZ,KAAKgI,YACjDrH,QAAQC,IACP,GAAGZ,KAAK4H,2BAA2B5H,KAAK6H,yBAAyB7H,KAAK8H,qBAAqB9H,KAAK+H,wBAEjGpF,QAAQuG,UAAS,KAChBvI,QAAQwI,QAAQ7J,EAAW,GAG5B,CAED,cAAAuJ,CAAepF,EAAiBC,GAC/B,OAAO,IAAIW,SAAQ,CAACC,EAASC,KAC5B,MAAM6E,EAAM,IAAI5F,EAAaC,EAAUC,EAAW1D,MAClDoJ,EACE7B,cAEAjC,MAAMC,IACNvF,KAAKqJ,aACJ,KACA,GAAGpF,EAAKE,KAAKV,EAAUC,SAAiB0F,EAAI1B,mBAE7CnC,EAAO3D,SAAS0H,GAAYtJ,KAAKqJ,aAAa,KAAM,KAAKC,EAAI9I,WAC7D8D,EAAQiB,EAAO,IAEfC,OAAO6C,IACPrI,KAAKqJ,aACJhB,EACA,GAAGpE,EAAKE,KAAKV,EAAUC,SAAiB2E,KAEzC9D,EAAO8D,EAAM,IAEbkB,SAAQ,IAAMH,EAAI9B,WAAU,GAE/B,CAED,YAAAkC,CAAa/F,EAAkBgG,GAC9B,MAAMC,EAAkC,GAIxC,OAHAD,EAAM7H,SAAQ,CAAC+H,EAAcC,IAC5BF,EAAY/H,KAAK3B,KAAK6I,eAAepF,EAAUkG,MAEzCtF,QAAQc,WAAWuE,EAC1B,CAED,mBAAAV,CAAoBvF,GACnB,OAAO,IAAIY,SAAQ,CAACC,EAASC,KAC5BwB,EAAG8D,QAAQpG,GAAU,CAACgC,EAAKgE,KAC1B,GAAIhE,EACHzF,KAAKqJ,cAAa,EAAM,IAAI5F,QAAegC,EAAIqE,cAC/CvF,EAAOkB,OACD,CACN,MAAMsE,EAAU,uCACVC,EAAWP,EAAMQ,QACrBN,GACiC,SAAjCA,EAAKlH,OAAO,GAAGuE,eACf+C,EAAQG,QAAQP,EAAKQ,UAAU,EAAG,IAAM,IAG1CnK,KAAK4H,WAAaoC,EAASxI,OACvBxB,KAAK4H,WAAa,EACrB5H,KAAKwJ,aAAa/F,EAAUuG,GAC1B1E,MAAM9E,GAAU8D,EAAQ9D,KACxBgF,OAAOC,GAAQlB,EAAOkB,MAExBzF,KAAKqJ,cAAa,EAAM,IAAI5F,2BAC5Ba,EAAQ,sBAET,IACA,GAEH,CAED,YAAA+E,CAAahB,EAAW+B,GACvBpK,KAAKgI,WAAWrG,KACf0G,EAAQ,aAAa+B,IAAW,eAAeA,IAEhD,CAED,cAAA/E,CAAegD,GACdA,EAAQrI,KAAK8H,cAAgB9H,KAAK6H,cAClC"}