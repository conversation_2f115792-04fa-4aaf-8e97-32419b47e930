import t from"util";import e from"fs";import s from"path";import i from"../../dist/pdfparser.js";const{pkInfo:r,_PARSER_SIG:n}=i;class o{args=[];aliases={};usageMsg="";parsedArgv=null;constructor(t){Array.isArray(t)&&(this.args=t)}usage(t){return this.usageMsg=`${t}\n\nOptions:\n`,this}alias(t,e,s){return this.aliases[t]={name:e,description:s},this}showHelp(){let t=this.usageMsg;for(const[e,s]of Object.entries(this.aliases)){const{name:i,description:r}=s;t+=`-${e},--${i}\t ${r}\n`}console.log(t)}get argv(){return this.parsedArgv?this.parsedArgv:this.parseArgv()}static isNumber(t){return"number"==typeof t||(!!/^0x[0-9a-f]+$/i.test(t)||/^[-+]?(?:\d+(?:\.\d*)?|\.\d+)(e[-+]?\d+)?$/.test(t))}setArg(t,e,s){const i=o.isNumber(e)?Number(e):e;this.setKey(s,t.split("."),i);const r=t in this.aliases?[this.aliases[t].name]:[];if(r.length<1)for(const[e,s]of Object.entries(this.aliases))if(t===s.name){r.push(e);break}r.forEach((t=>this.setKey(s,t.split("."),i)))}setKey(t,e,s){let i=t;for(let t=0;t<e.length-1;t++){const s=e[t];if("__proto__"===s)return;void 0===i[s]&&(i[s]={}),i[s]!==Object.prototype&&i[s]!==Number.prototype&&i[s]!==String.prototype||(i[s]={}),i[s]===Array.prototype&&(i[s]=[]),i=i[s]}const r=e[e.length-1];"__proto__"!==r&&(i!==Object.prototype&&i!==Number.prototype&&i!==String.prototype||(i={}),i===Array.prototype&&(i=[]),void 0===i[r]?i[r]=s:Array.isArray(i[r])?i[r].push(s):i[r]=[i[r],s])}parseArgv(){const{args:t}=this,e={};for(let s=0;s<t.length;s++){const i=t[s];if(/^--.+/.test(i)){const r=i.match(/^--(.+)/);if(!Array.isArray(r)){console.warn("Unknow CLI options:",i);continue}const n=r[1],o=t[s+1];void 0===o||/^-/.test(o)?/^(true|false)$/.test(o)?(this.setArg(n,"true"===o,e),s++):this.setArg(n,!0,e):(this.setArg(n,o,e),s++)}else if(/^-[^-]+/.test(i)){const r=i.slice(-1)[0];"-"!==r&&(t[s+1]&&!/^(-|--)[^-]/.test(t[s+1])?(this.setArg(r,t[s+1],e),s++):t[s+1]&&/^(true|false)$/.test(t[s+1])?(this.setArg(r,"true"===t[s+1],e),s++):this.setArg(r,!0,e))}else console.warn("Unknow CLI options:",i)}return this.parsedArgv=e,e}}const a=new o(process.argv.slice(2)).usage(`\n${n}\n\nUsage: ${r.name} -f|--file [-o|output_dir]`).alias("v","version","Display version.").alias("h","help","Display brief help information.").alias("f","file","(required) Full path of input PDF file or a directory to scan for all PDF files.\n\t\t When specifying a PDF file name, it must end with .PDF, otherwise it would be treated as a input directory.").alias("o","output","(optional) Full path of output directory, must already exist.\n\t\t Current JSON file in the output folder will be replaced when file name is same.").alias("s","silent","(optional) when specified, will only log errors, otherwise verbose.").alias("t","fieldTypes","(optional) when specified, will generate .fields.json that includes fields ids and types.").alias("c","content","(optional) when specified, will generate .content.txt that includes text content from PDF.").alias("m","merge","(optional) when specified, will generate .merged.json that includes auto-merged broken text blocks from PDF.").alias("r","stream","(optional) when specified, will process and parse with buffer/object transform stream rather than file system."),{ParserStream:u,StringifyStream:l,pkInfo:p,_PARSER_SIG:h}=i,{argv:c}=a,d="v"in c,f="h"in c,m="s"in c?0:5,g="f"in c,P="c"in c,y="t"in c,w="m"in c,S="r"in c,F=c.f;class ${inputDir="";inputFile="";inputPath="";outputDir="";outputFile="";outputPath="";pdfParser=null;curCLI=null;constructor(t,e,i){this.inputDir=s.normalize(t),this.inputFile=e,this.inputPath=s.join(this.inputDir,this.inputFile),this.outputDir=s.normalize(c.o||t),this.pdfParser=null,this.curCLI=i}generateMergedTextBlocksStream(){return new Promise(((t,e)=>{if(!this.pdfParser)return void e("PDFParser instance is not available.");const s=u.createOutputStream(this.outputPath.replace(".json",".merged.json"),t,e);this.pdfParser.getMergedTextBlocksStream().pipe(new l).pipe(s)}))}generateRawTextContentStream(){return new Promise(((t,e)=>{const s=u.createOutputStream(this.outputPath.replace(".json",".content.txt"),t,e);this.pdfParser.getRawTextContentStream().pipe(s)}))}generateFieldsTypesStream(){return new Promise(((t,e)=>{const s=u.createOutputStream(this.outputPath.replace(".json",".fields.json"),t,e);this.pdfParser.getAllFieldsTypesStream().pipe(new l).pipe(s)}))}processAdditionalStreams(){const t=[];return y&&t.push(this.generateFieldsTypesStream()),P&&t.push(this.generateRawTextContentStream()),w&&t.push(this.generateMergedTextBlocksStream()),Promise.allSettled(t)}onPrimarySuccess(t,e){this.curCLI.addResultCount(!1),this.processAdditionalStreams().then((e=>t(e))).catch((t=>e(t)))}onPrimaryError(t,e){this.curCLI.addResultCount(t),e(t)}parseOnePDFStream(){return new Promise(((t,s)=>{this.pdfParser=new i(null,P),this.pdfParser.on("pdfParser_dataError",(t=>this.onPrimaryError(t.parserError,s)));const r=e.createWriteStream(this.outputPath);r.on("finish",(()=>this.onPrimarySuccess(t,s))),r.on("error",(t=>this.onPrimaryError(t,s))),console.info(`Transcoding Stream ${this.inputFile} to - ${this.outputPath}`);e.createReadStream(this.inputPath).pipe(this.pdfParser.createParserStream()).pipe(new l).pipe(r)}))}parseOnePDF(){return new Promise(((t,s)=>{this.pdfParser=new i(null,P),this.pdfParser.on("pdfParser_dataError",(t=>{this.onPrimaryError(t.parserError,s)})),this.pdfParser.on("pdfParser_dataReady",(i=>{e.writeFile(this.outputPath,JSON.stringify(i),(e=>{e?this.onPrimaryError(e,s):this.onPrimarySuccess(t,s)}))})),console.info(`Transcoding File ${this.inputFile} to - ${this.outputPath}`),this.pdfParser.loadPDF(this.inputPath,m)}))}async validateParams(){let t="";if(e.existsSync(this.inputDir))if(e.existsSync(this.inputPath)){if(!e.existsSync(this.outputDir))try{await e.promises.mkdir(this.outputDir,{recursive:!0})}finally{e.existsSync(this.outputDir)||(t=`Input error: output directory doesn't exist and fails to create - ${this.outputDir}.`)}}else t=`Input error: input file doesn't exist - ${this.inputPath}.`;else t=`Input error: input directory doesn't exist - ${this.inputDir}.`;if(""!==t)return this.curCLI.addResultCount(t),t;const i=s.extname(this.inputFile).toLowerCase();if(".pdf"!==i)t=`Input error: input file name doesn't have pdf extention  - ${this.inputFile}.`;else if(this.outputFile=`${s.basename(this.inputPath,i)}.json`,this.outputPath=s.normalize(`${this.outputDir}/${this.outputFile}`),e.existsSync(this.outputPath))console.warn(`Output file will be replaced - ${this.outputPath}`);else{const s=e.openSync(this.outputPath,"wx");s?(e.closeSync(s),e.unlinkSync(this.outputPath)):t=`Input error: can not write to ${this.outputPath}`}return t}destroy(){this.inputDir="",this.inputFile="",this.inputPath="",this.outputDir="",this.outputPath="",this.pdfParser&&this.pdfParser.destroy(),this.pdfParser=null,this.curCLI=null}processFile(){return new Promise(((t,e)=>{this.validateParams().then((s=>{if(""!==s)e(s);else{(S?this.parseOnePDFStream:this.parseOnePDF).call(this).then((e=>t(e))).catch((t=>e(t)))}})).catch((t=>e(t)))}))}getOutputFile=()=>s.join(this.outputDir,this.outputFile)}class C{inputCount=0;successCount=0;failedCount=0;warningCount=0;statusMsgs=[];constructor(){this.inputCount=0,this.successCount=0,this.failedCount=0,this.warningCount=0,this.statusMsgs=[]}initialize(){t.verbosity(m);let e=!0;try{d?(console.log(p.version),e=!1):f?(a.showHelp(),e=!1):g||(a.showHelp(),console.error("-f is required to specify input directory or file."),e=!1)}catch(t){console.error(`Exception: ${t.message}`),e=!1}return e}async start(){if(this.initialize()&&F){console.log(h),console.time(h);try{const t=e.statSync(F);t.isFile()?(this.inputCount=1,await this.processOneFile(s.dirname(F),s.basename(F))):t.isDirectory()&&await this.processOneDirectory(s.normalize(F))}catch(t){console.error("Exception: ",t)}finally{this.complete()}}else console.error("Invalid input parameters.")}complete(){this.statusMsgs.length>0&&console.log(this.statusMsgs),console.log(`${this.inputCount} input files\t${this.successCount} success\t${this.failedCount} fail\t${this.warningCount} warning`),process.nextTick((()=>{console.timeEnd(h)}))}processOneFile(t,e){return new Promise(((i,r)=>{const n=new $(t,e,this);n.processFile().then((r=>{this.addStatusMsg(null,`${s.join(t,e)} => ${n.getOutputFile()}`),r.forEach((t=>this.addStatusMsg(null,`+ ${t.value}`))),i(r)})).catch((i=>{this.addStatusMsg(i,`${s.join(t,e)} => ${i}`),r(i)})).finally((()=>n.destroy()))}))}processFiles(t,e){const s=[];return e.forEach(((e,i)=>s.push(this.processOneFile(t,e)))),Promise.allSettled(s)}processOneDirectory(t){return new Promise(((s,i)=>{e.readdir(t,((e,r)=>{if(e)this.addStatusMsg(!0,`[${t}] - ${e.toString()}`),i(e);else{const e="!@#$%^&*()+=[]\\';,/{}|\":<>?~`.-_  ",n=r.filter((t=>".pdf"===t.slice(-4).toLowerCase()&&e.indexOf(t.substring(0,1))<0));this.inputCount=n.length,this.inputCount>0?this.processFiles(t,n).then((t=>s(t))).catch((t=>i(t))):(this.addStatusMsg(!0,`[${t}] - No PDF files found`),s("no pdf files found"))}}))}))}addStatusMsg(t,e){this.statusMsgs.push(t?`✗ Error : ${e}`:`✓ Success : ${e}`)}addResultCount(t){t?this.failedCount++:this.successCount++}}export{C as default};
//# sourceMappingURL=pdfparser_cli.js.map
