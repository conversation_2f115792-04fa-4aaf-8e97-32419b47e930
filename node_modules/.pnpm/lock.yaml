lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      pdf2json:
        specifier: ^3.2.0
        version: 3.2.0
      pdfreader:
        specifier: ^3.0.7
        version: 3.0.7

packages:

  pdf2json@3.1.4:
    resolution: {integrity: sha512-rS+VapXpXZr+5lUpHmRh3ugXdFXp24p1RyG24yP1DMpqP4t0mrYNGpLtpSbWD42PnQ59GIXofxF+yWb7M+3THg==}
    engines: {node: '>=18.12.1', npm: '>=8.19.2'}
    hasBin: true
    bundledDependencies:
      - '@xmldom/xmldom'

  pdf2json@3.2.0:
    resolution: {integrity: sha512-5RJYU5zWFXTQ5iRXAo75vlhK5ybZOyqEyg/szw2VtHc6ZOPcC7ruX4nnXk1OqqlY56Z7XT+WCFhV+/XPj4QwtQ==}
    engines: {node: '>=20.18.0'}
    hasBin: true
    bundledDependencies: []

  pdfreader@3.0.7:
    resolution: {integrity: sha512-68Htw7su6HDJGGKv9tkjilRyf8zaHulEKRCgCwx4FE8krcMB8iBtM46Smjjez0jFm45dUKYXJzThyLwCqfQlCQ==}
    engines: {node: '>=14'}

snapshots:

  pdf2json@3.1.4: {}

  pdf2json@3.2.0: {}

  pdfreader@3.0.7:
    dependencies:
      pdf2json: 3.1.4
