{"name": "pdfreader", "type": "module", "version": "3.0.7", "description": "Read text and parse tables from PDF files. Supports tabular data with automatic column detection, and rule-based parsing.", "main": "dist/index.cjs", "module": "./index.js", "typings": "./index.d.ts", "scripts": {"prettier:print": "prettier --list-different \"./**/*.js\" \"./**/*.md\" \"./**/*.d.ts\"", "prettier:check": "npm run -s prettier:print 1>&2; exit $(npm run -s prettier:print | wc -l)", "prettier:fix": "prettier \"./**/*.js\" \"./**/*.md\" \"./**/*.d.ts\" --write --end-of-line lf", "test:samples": "node parse.js test/sample.pdf && node parseAsBuffer.js test/sample.pdf", "test:update": "ava --update-snapshots", "test": "ava", "lint": "eslint .", "semantic-release": "semantic-release", "build:cjs": "rollup index.js --file dist/index.cjs --format cjs"}, "repository": {"type": "git", "url": "https://github.com/adrienjoly/npm-pdfreader"}, "keywords": ["pdf", "reader", "parser", "parse", "parsing", "convert", "CLI", "table", "data", "csv", "json", "rules"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/adrienjoly/npm-pdfreader/issues"}, "homepage": "https://github.com/adrienjoly/npm-pdfreader", "dependencies": {"pdf2json": "3.1.4"}, "devDependencies": {"@semantic-release/changelog": "^6.0.1", "@semantic-release/git": "^10.0.1", "@semantic-release/npm": "^9.0.1", "ava": "^4.1.0", "eslint": "^8.11.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "execa": "^6.1.0", "prettier": "2.6.1", "semantic-release": "^19.0.2", "rollup": "^4.19.1"}, "engines": {"node": ">=14"}}