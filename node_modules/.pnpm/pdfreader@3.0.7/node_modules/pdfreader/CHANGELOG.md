## [1.2.1](https://github.com/adrienjoly/npm-pdfreader/compare/v1.2.0...v1.2.1) (2020-09-25)


### Bug Fixes

* **deps:** Update dependencies ([#63](https://github.com/adrienjoly/npm-pdfreader/issues/63)) ([308f322](https://github.com/adrienjoly/npm-pdfreader/commit/308f322ea670ab2ec11f77e3588667674709b453))

# [1.2.0](https://github.com/adrienjoly/npm-pdfreader/compare/v1.1.4...v1.2.0) (2020-09-25)


### Features

* Support password-protected PDF files ([#61](https://github.com/adrienjoly/npm-pdfreader/issues/61)) ([248af89](https://github.com/adrienjoly/npm-pdfreader/commit/248af89d79304dfa64b5785614b496e4e5d36e69)), closes [#15](https://github.com/adrienjoly/npm-pdfreader/issues/15)

## [1.1.4](https://github.com/adrienjoly/npm-pdfreader/compare/v1.1.3...v1.1.4) (2020-09-25)


### Bug Fixes

* Ease contributions ([#62](https://github.com/adrienjoly/npm-pdfreader/issues/62)) ([4a1fe66](https://github.com/adrienjoly/npm-pdfreader/commit/4a1fe6677d5a829049aa0c3c28dccb2f96e8e2f6))

## [1.1.3](https://github.com/adrienjoly/npm-pdfreader/compare/v1.1.2...v1.1.3) (2020-04-26)


### Bug Fixes

* **node:** use latest node 10 version ([#52](https://github.com/adrienjoly/npm-pdfreader/issues/52)) ([eb34ea9](https://github.com/adrienjoly/npm-pdfreader/commit/eb34ea92fea924d3d1e28b13a2e730b62a996b51))

## [1.1.2](https://github.com/adrienjoly/npm-pdfreader/compare/v1.1.1...v1.1.2) (2020-04-26)


### Bug Fixes

* **deps:** with npm audit fix ([#51](https://github.com/adrienjoly/npm-pdfreader/issues/51)) ([16502fc](https://github.com/adrienjoly/npm-pdfreader/commit/16502fce29af76ebf8216e17aafb388a54326b6c))

## [1.1.1](https://github.com/adrienjoly/npm-pdfreader/compare/v1.1.0...v1.1.1) (2020-04-26)


### Bug Fixes

* **deps:** bump acorn from 6.3.0 to 6.4.1 ([#46](https://github.com/adrienjoly/npm-pdfreader/issues/46)) ([af61802](https://github.com/adrienjoly/npm-pdfreader/commit/af61802d1430adab8c9c56588d8a5b565910bd3a))

# [1.1.0](https://github.com/adrienjoly/npm-pdfreader/compare/v1.0.10...v1.1.0) (2020-04-26)


### Features

* **deps:** upgrade pdf2json to version 1.2.0 ([#50](https://github.com/adrienjoly/npm-pdfreader/issues/50)) ([0877162](https://github.com/adrienjoly/npm-pdfreader/commit/08771623aa7bf228b4a39e763e38614e79dca10c)), closes [#40](https://github.com/adrienjoly/npm-pdfreader/issues/40)

## [1.0.10](https://github.com/adrienjoly/npm-pdfreader/compare/v1.0.9...v1.0.10) (2020-04-26)


### Bug Fixes

* **ci:** check formatting in separate step, after tests ([#49](https://github.com/adrienjoly/npm-pdfreader/issues/49)) ([9129b8a](https://github.com/adrienjoly/npm-pdfreader/commit/9129b8a4f860fbc674fd7485c7c0661c0344a71d))

## [1.0.9](https://github.com/adrienjoly/npm-pdfreader/compare/v1.0.8...v1.0.9) (2020-04-26)


### Bug Fixes

* **ci:** prettier to ignore CHANGELOG (generated) ([8bcf776](https://github.com/adrienjoly/npm-pdfreader/commit/8bcf77674a6e472c791accca4d8385e8462679b6))
* **ci:** skip github actions workflow on release commits ([c970cda](https://github.com/adrienjoly/npm-pdfreader/commit/c970cda451a3a3b53c9d42c721524b22a7714544))

## [1.0.8](https://github.com/adrienjoly/npm-pdfreader/compare/v1.0.7...v1.0.8) (2020-04-26)


### Bug Fixes

* **release:** automatic update of version in package.json ([#48](https://github.com/adrienjoly/npm-pdfreader/issues/48)) ([bad1d5b](https://github.com/adrienjoly/npm-pdfreader/commit/bad1d5bfce1c55b503cca3380c3187fb071b6056))
