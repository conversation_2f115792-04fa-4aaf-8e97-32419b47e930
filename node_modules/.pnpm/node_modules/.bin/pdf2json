#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Documents/code/hono/pdfToTable/node_modules/.pnpm/pdf2json@3.1.4/node_modules/pdf2json/bin/node_modules:/Users/<USER>/Documents/code/hono/pdfToTable/node_modules/.pnpm/pdf2json@3.1.4/node_modules/pdf2json/node_modules:/Users/<USER>/Documents/code/hono/pdfToTable/node_modules/.pnpm/pdf2json@3.1.4/node_modules:/Users/<USER>/Documents/code/hono/pdfToTable/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Documents/code/hono/pdfToTable/node_modules/.pnpm/pdf2json@3.1.4/node_modules/pdf2json/bin/node_modules:/Users/<USER>/Documents/code/hono/pdfToTable/node_modules/.pnpm/pdf2json@3.1.4/node_modules/pdf2json/node_modules:/Users/<USER>/Documents/code/hono/pdfToTable/node_modules/.pnpm/pdf2json@3.1.4/node_modules:/Users/<USER>/Documents/code/hono/pdfToTable/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../pdf2json/bin/pdf2json.js" "$@"
else
  exec node  "$basedir/../pdf2json/bin/pdf2json.js" "$@"
fi
